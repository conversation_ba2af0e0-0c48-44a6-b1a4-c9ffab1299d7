Vue.component("PaxSelector", {
  template: `
    <div class="pax-selector-overlay" v-show="visible" @click="handleOverlayClick">
      <div class="pax-selector-container" @click.stop>
        <div class="pax-selector-header">
          <h3 class="pax-selector-title">您好，请选择就餐人数</h3>
          <span class="pax-selector-table-info">桌号--</span>
        </div>
        
        <div class="pax-selector-content">
          <div class="pax-number-grid">
            <button 
              v-for="(item, index) in gridItems" 
              :key="index"
              class="pax-number-btn"
              :class="{ 
                'selected': item.type === 'number' ? (selectedPax === item.value && !showOtherInput) : showOtherInput,
                'empty': item.type === 'empty',
                'other': item.type === 'other'
              }"
              @click="item.type === 'number' ? selectPax(item.value) : (item.type === 'other' ? selectOther() : null)"
              :disabled="item.type === 'empty'"
            >
              {{ item.type === 'empty' ? '' : item.label }}
            </button>
          </div>
          
          <div v-if="showOtherInput" class="pax-other-input-wrapper">
            <input 
              type="number" 
              v-model.number="customPax"
              class="pax-other-input"
              placeholder="请输入人数"
              min="1"
              max="99"
              @input="handleCustomPaxInput"
            >
          </div>
        </div>
        
        <div class="pax-selector-footer">
          <button 
            class="pax-confirm-btn"
            :class="{ 'disabled': !isValid, 'animate': isValid }"
            :disabled="!isValid"
            @click="confirm"
          >
            开始点餐
          </button>
        </div>
      </div>
    </div>
  `,

  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      selectedPax: null,
      showOtherInput: false,
      customPax: null
    }
  },

  computed: {
    gridItems() {
      const items = []
      // 生成1-9数字
      for (let i = 1; i <= 9; i++) {
        items.push({ type: "number", value: i, label: i.toString() })
      }
      // 添加"其他"选项
      items.push({ type: "other", label: "其他" })

      return items
    },

    isValid() {
      if (this.showOtherInput) {
        return this.customPax && this.customPax > 0 && this.customPax <= 99
      }
      return this.selectedPax && this.selectedPax > 0
    },

    finalPax() {
      return this.showOtherInput ? this.customPax : this.selectedPax
    }
  },

  methods: {
    selectPax(num) {
      this.selectedPax = num
      this.showOtherInput = false
      this.customPax = null
    },

    selectOther() {
      this.showOtherInput = true
      this.selectedPax = null
      this.$nextTick(() => {
        const input = this.$el.querySelector(".pax-other-input")
        if (input) {
          input.focus()
        }
      })
    },

    handleCustomPaxInput() {
      // 确保输入值在有效范围内
      if (this.customPax > 99) {
        this.customPax = 99
      } else if (this.customPax < 1) {
        this.customPax = 1
      }
    },

    handleOverlayClick() {
      // 点击遮罩层不关闭，需要用户主动选择
      return false
    },

    confirm() {
      if (this.isValid) {
        this.$emit("pax-selected", this.finalPax)
        this.close()
      }
    },

    close() {
      this.$emit("close")
    },

    reset() {
      this.selectedPax = null
      this.showOtherInput = false
      this.customPax = null
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        this.reset()
      }
    }
  }
})
