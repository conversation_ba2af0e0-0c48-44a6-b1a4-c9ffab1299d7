/* ==================== 人数选择器组件样式 ==================== */

.pax-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(0.25rem);
  -webkit-backdrop-filter: blur(0.25rem);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  box-sizing: border-box;
}

.pax-selector-container {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 0.1875rem 0.75rem rgba(0, 0, 0, 0.15);
  width: 80%;
  max-width: 20rem;
  max-height: 90vh;
  overflow: hidden;
  animation: paxSelectorSlideIn 0.3s ease-out;
}

@keyframes paxSelectorSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(0.25rem);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.pax-selector-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0.5rem 0.25rem;
}

.pax-selector-title {
  margin: 0;
  font-size: 0.5rem;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
  text-align: center;
}

.pax-selector-content {
  padding: 0 0.5rem 0.375rem;
}

.pax-number-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 0.125rem;
  margin-bottom: 0.25rem;
}

.pax-number-btn {
  background: #f8f9fa;
  border: 0.0625rem solid #e9ecef;
  border-radius: 0.1875rem;
  padding: 0.1875rem 0.125rem;
  font-size: 0.4375rem;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.pax-number-btn.empty {
  background: transparent;
  border: none;
  cursor: default;
  pointer-events: none;
}

.pax-number-btn.other {
  /* 其他按钮的特殊样式 */
}

.pax-number-btn:hover:not(.empty):not(:disabled) {
  background-color: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-0.03125rem);
}

.pax-number-btn.selected {
  background-color: #40c9a2;
  border-color: #40c9a2;
  color: white;
  transform: translateY(-0.03125rem);
  box-shadow: 0 0.03125rem 0.1875rem rgba(64, 201, 162, 0.3);
}

.pax-other-input-wrapper {
  margin-top: 0.1875rem;
  animation: paxInputSlideDown 0.3s ease-out;
}

@keyframes paxInputSlideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-0.125rem);
  }
  to {
    opacity: 1;
    max-height: 1.5rem;
    transform: translateY(0);
  }
}

.pax-other-input {
  width: 100%;
  padding: 0.1875rem 0.25rem;
  border: 0.0625rem solid #e9ecef;
  border-radius: 0.125rem;
  font-size: 0.375rem;
  color: #495057;
  background: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.pax-other-input:focus {
  outline: none;
  border-color: #40c9a2;
  box-shadow: 0 0 0 0.03125rem rgba(64, 201, 162, 0.2);
}

.pax-other-input::placeholder {
  color: #adb5bd;
}

.pax-selector-footer {
  padding: 0 0.5rem 0.5rem;
}

.pax-confirm-btn {
  width: 100%;
  background-color: white;
  border: 0.0625rem solid var(--styleColor, #40c9a2);
  border-radius: 0.375rem;
  padding: 0.1875rem 0.375rem;
  font-size: 0.4375rem;
  font-weight: 500;
  color: var(--styleColor, #40c9a2);
  cursor: pointer;
  transition: all 0.3s ease;
  height: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.pax-confirm-btn:hover:not(.disabled) {
  background-color: var(--styleColor, #40c9a2);
  color: white;
  transform: translateY(-0.03125rem);
  box-shadow: 0 0.0625rem 0.1875rem rgba(64, 201, 162, 0.3);
}

.pax-confirm-btn.animate {
  animation: buttonPulse 2s ease-in-out infinite;
}

@keyframes buttonPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0.0625rem 0.1875rem rgba(64, 201, 162, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0.125rem 0.25rem rgba(64, 201, 162, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0.0625rem 0.1875rem rgba(64, 201, 162, 0.3);
  }
}

.pax-confirm-btn:active:not(.disabled) {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.pax-confirm-btn.disabled {
  background-color: #dee2e6;
  color: #adb5bd;
  border-color: #dee2e6;
  cursor: not-allowed;
  animation: none;
  transform: none;
  box-shadow: none;
}

/* 只为iPhone 6/7/8优化 */
@media only screen and (device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) {
  .pax-selector-container {
    width: 80%;
    max-width: 18rem;
  }

  .pax-selector-header {
    padding: 0.25rem 0.375rem 0.1875rem;
  }

  .pax-selector-title {
    font-size: 0.4375rem;
  }

  .pax-selector-content {
    padding: 0 0.375rem 0.25rem;
  }

  .pax-number-grid {
    gap: 0.09375rem;
    margin-bottom: 0.1875rem;
  }

  .pax-number-btn {
    padding: 0.125rem 0.09375rem;
    font-size: 0.375rem;
    height: 0.75rem;
    border-radius: 0.125rem;
  }

  .pax-other-input {
    padding: 0.125rem 0.1875rem;
    font-size: 0.3125rem;
    border-radius: 0.09375rem;
  }

  .pax-confirm-btn {
    padding: 0.125rem 0.25rem;
    font-size: 0.375rem;
    height: 0.75rem;
    border-radius: 0.25rem;
  }

  .pax-selector-footer {
    padding: 0 0.375rem 0.375rem;
  }
}
